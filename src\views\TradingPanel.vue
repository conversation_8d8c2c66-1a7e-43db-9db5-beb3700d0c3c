<template>
  <div class="trading-panel-container">
    <!-- 主交易面板 -->
    <div class="trading-panel">
      <!-- 左侧操作列 -->
      <div class="left-control-panel">
        <!-- 合约代码显示 -->
        <div class="contract-code-display">
          {{ currentContract?.code || panelContract?.code || '未选择' }}
        </div>

        <!-- 缩放控制 -->
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn">-</button>
          <button @click="zoomIn" class="zoom-btn">+</button>
        </div>

        <!-- 价格变化信息 -->
        <div class="price-info-section">
          <div class="price-change-display">
            <div class="price-change-value" :class="{ negative: priceChangePercent.startsWith('-'), positive: !priceChangePercent.startsWith('-') && priceChangePercent !== '' }">{{ priceChangePercent || '0.00' }}%</div>
          </div>

          <div class="market-stats">
            <div class="stat-item">{{ totalVolume || 0 }}</div>
            <div class="stat-item">{{ totalPosition || 0 }}</div>
            <div class="stat-item">{{ dailyPositionChange || 0 }}</div>
          </div>
        </div>

        <!-- 红蓝数值显示 -->
        <div class="zero-values">
          <div class="zero-value red">{{ redValue || 0 }}</div>
          <div class="zero-value blue">{{ blueValue || 0 }}</div>
        </div>

        <!-- 下单数量输入 -->
        <div class="order-inputs">
          <div class="input-group">
            <input v-model="lightOrderQuantity" type="number" class="order-input" placeholder="1" min="1" max="5"
                   title="轻仓下单数量（手）- 鼠标左键下单" />
          </div>
          <div class="input-group">
            <input v-model="heavyOrderQuantity" type="number" class="order-input" placeholder="20" min="5" max="25"
                   title="重仓下单数量（手）- 鼠标右键下单" />
          </div>
        </div>

        <!-- 订单类型选择 -->
        <div class="order-type-group">
          <label class="radio-label">
            <input type="radio" v-model="orderType" value="A" />
            <span class="radio-text">Order(A)</span>
          </label>
          <label class="radio-label">
            <input type="radio" v-model="orderType" value="B" />
            <span class="radio-text">Order(B)</span>
          </label>
        </div>

        <!-- 交易选项 -->
        <div class="order-options">
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.autoHand" />
            <span class="checkbox-text">金手指！</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.cLimit345" />
            <span class="checkbox-text">CLimit 345</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.cLimit550" />
            <span class="checkbox-text">CLimit 550</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.noLimit" />
            <span class="checkbox-text">No Limit</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.noCombo" />
            <span class="checkbox-text">NoCombo</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" v-model="options.upLimit" />
            <span class="checkbox-text">UpLimit</span>
          </label>
        </div>

        <!-- 持仓信息 -->
        <div class="position-info-section">
          <div class="position-line" title="净持仓 = 多头持仓 - 空头持仓">净仓: {{ netPosition || 0 }}</div>
          <div class="position-line" title="C: 平仓相关持仓, T: 今日持仓">C: {{ cPosition || 0 }} T: {{ tPosition || 0 }}</div>
        </div>

        <!-- 盈亏显示 -->
        <div class="pnl-display">
          <div class="pnl-value">{{ pnlValue || 0 }}</div>
          <div class="pnl-letter">P</div>
        </div>

      </div>

      <!-- 右侧五列表格 -->
      <div class="price-table-container">
        <div class="price-table" ref="tableContainer">
          <!-- 卖盘数据区域（红色区域，在黑条上方） -->
          <div class="sell-orders-section">
            <div
              v-for="(item, index) in sellOrders"
              :key="`sell-${index}`"
              class="price-row sell-row"
            >
              <!-- 第一列：撤单 -->
              <div class="cancel-col"></div>

              <!-- 第二列：买量 -->
              <div
                class="buy-volume-col clickable"
                @click="handleOrderClick('buy', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('buy', item, index, $event)"
                :class="{ active: isSelected('sell', 'buy', index) }"
                title="左键轻仓买入，右键重仓买入"
              >
                {{ item.buyVolume || '' }}
              </div>

              <!-- 第三列：价格 -->
              <div class="price-col market-price">
                {{ item.price || '' }}
              </div>

              <!-- 第四列：卖量 -->
              <div
                class="sell-volume-col clickable"
                @click="handleOrderClick('sell', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('sell', item, index, $event)"
                :class="{ active: isSelected('sell', 'sell', index) }"
                title="左键轻仓卖出，右键重仓卖出"
              >
                {{ item.sellVolume || '' }}
              </div>

              <!-- 第五列：总量 -->
              <div class="total-volume-col">
                {{ getTotalVolume(item) }}
              </div>
            </div>
          </div>

          <!-- 中间分隔线 -->
          <div class="price-separator"></div>

          <!-- 买盘数据区域（蓝色区域，在黑条下方） -->
          <div class="buy-orders-section">
            <div
              v-for="(item, index) in buyOrders"
              :key="`buy-${index}`"
              class="price-row buy-row"
            >
              <!-- 第一列：撤单 -->
              <div class="cancel-col"></div>

              <!-- 第二列：买量 -->
              <div
                class="buy-volume-col clickable"
                @click="handleOrderClick('buy', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('buy', item, index, $event)"
                :class="{ active: isSelected('buy', 'buy', index) }"
                title="左键轻仓买入，右键重仓买入"
              >
                {{ item.buyVolume || '' }}
              </div>

              <!-- 第三列：价格 -->
              <div class="price-col market-price">
                {{ item.price || '' }}
              </div>

              <!-- 第四列：卖量 -->
              <div
                class="sell-volume-col clickable"
                @click="handleOrderClick('sell', item, index, $event)"
                @contextmenu.prevent="handleOrderClick('sell', item, index, $event)"
                :class="{ active: isSelected('buy', 'sell', index) }"
                title="左键轻仓卖出，右键重仓卖出"
              >
                {{ item.sellVolume || '' }}
              </div>

              <!-- 第五列：总量 -->
              <div class="total-volume-col">
                {{ getTotalVolume(item) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ctpService } from '../services/ctpService'
import { OrderRequest } from '../types/ctp'

import { getCurrentWebviewWindow } from '@tauri-apps/api/webviewWindow'
import { useContractStore } from '@/stores/contractStore'
import type { ContractInfo } from '@/types/trading'
import { contractService } from '../services/contractService'


// 从URL参数中获取合约代码
const getContractCodeFromUrl = (): string | null => {
  const urlParams = new URLSearchParams(window.location.hash.split('?')[1] || '')
  return urlParams.get('contract')
}

interface OrderData {
  price: number
  buyVolume: number
  sellVolume: number
  level: string
}

interface SelectedCell {
  type: 'sell' | 'buy'
  field: 'cancel' | 'buy' | 'sell' | 'price'
  value: number
  data: OrderData
  index: number
  quantity?: number  // 下单数量（可选）
}

const isUsingRealData = ref(false)

// 合约状态管理
const { currentContract, setCurrentContract } = useContractStore()
const panelContract = ref<ContractInfo | null>(null)

// 窗口ID管理 - 每个TradingPanel窗口的唯一标识
const windowId = ref<string>('')

// 交易相关
const selectedCell = ref<SelectedCell | null>(null)  // 当前选中的单元格（用于下单/撤单）

const lightOrderQuantity = ref(1)                   // 轻仓下单数量（手）- 鼠标左键
const heavyOrderQuantity = ref(20)                  // 重仓下单数量（手）- 鼠标右键
const orderPrice = ref(20)                          // 下单价格（点击价格档位时自动填入）
const orderType = ref('A')                          // 订单类型：A=默认模式, B=特殊模式

// 交易选项配置
const options = ref({
  autoHand: false,                          // 自动手数：是否自动计算下单手数
  cLimit345: false,                         // C限制345：特定的交易限制规则
  cLimit550: false,                         // C限制550：另一种交易限制规则
  noLimit: false,                           // 无限制：取消所有交易限制
  noCombo: false,                           // 无组合：禁用组合交易功能
  upLimit: false                            // 涨停限制：涨停价格限制开关
})

// 持仓信息
const netPosition = ref(0)               // 净持仓（多头-空头的净值）
const cPosition = ref(0)                    // C仓位（可能指Close平仓相关持仓）
const tPosition = ref(0)                    // T仓位（可能指Today今日持仓）
const pnlValue = ref(0)                     // 盈亏值（Profit and Loss）

// 界面缩放
const fontSize = ref(11)                    // 字体大小（像素）
const cellHeight = ref(18)                  // 单元格高度（像素）



// 当前价格和表格控制
const currentPrice = ref(0)           // 当前价格/最新价（等待行情数据）
const tableContainer = ref<HTMLElement>()   // 表格容器引用
const priceDirection = ref<'up' | 'down' | 'neutral'>('neutral')  // 价格变化方向

// 市场数据 - 使用真实行情数据，初始值为0等待行情更新
const priceChangePercent = ref('')           // 价格变化百分比（相对于昨结算价）- 从真实行情计算
const totalVolume = ref(0)                  // 总成交量（手）- 从CTP行情数据获取
const totalPosition = ref(0)                // 总持仓量（手）- 从CTP行情数据获取
const dailyPositionChange = ref(0)          // 日内持仓变化（手）- 从CTP行情数据计算
const redValue = ref(0)                     // 红色数值显示（用于特殊标记）
const blueValue = ref(0)                    // 蓝色数值显示（用于特殊标记）




// 动态生成的卖盘数据（当前价格之上27档）
const sellOrders = ref<OrderData[]>([])

// 动态生成的买盘数据（当前价格之下27档）
const buyOrders = ref<OrderData[]>([])

// 价格档位配置（固定显示档位数）
const PRICE_LEVELS = {
  SELL_LEVELS: 20,  // 卖盘档位数（黑条上方红色区域）
  BUY_LEVELS: 20,   // 买盘档位数（黑条下方蓝色区域）
  PRICE_STEP: 1     // 价格步长
}

// 存储真实行情数据的映射表（价格 -> 买卖量）
const marketDataMap = ref<Map<number, { bidVolume: number, askVolume: number }>>(new Map())

// 数据更新锁，防止并发修改
let isUpdatingOrders = false

// 根据当前价格动态生成价格档位数据
const generatePriceOrders = (centerPrice: number) => {
  if (isUpdatingOrders) {
    console.log('⚠️ 正在更新档位数据，跳过重复生成');
    return;
  }

  isUpdatingOrders = true;
  console.log('🔄 根据当前价格生成档位数据:', centerPrice);

  try {
    // 生成卖盘数据（当前价格之上）- 按价格从高到低排序
    const newSellOrders: OrderData[] = [];
    for (let i = 1; i <= PRICE_LEVELS.SELL_LEVELS; i++) {
      const price = centerPrice + i * PRICE_LEVELS.PRICE_STEP;
      const marketData = marketDataMap.value.get(price);

      newSellOrders.push({
        price: price,
        buyVolume: 0,
        sellVolume: marketData?.askVolume || 0,
        level: i.toString()
      });
    }
    // 卖盘按价格从高到低排序（倒序）
    newSellOrders.reverse();

    // 生成买盘数据（当前价格之下）
    const newBuyOrders: OrderData[] = [];
    for (let i = 1; i <= PRICE_LEVELS.BUY_LEVELS; i++) {
      const price = centerPrice - i * PRICE_LEVELS.PRICE_STEP;
      const marketData = marketDataMap.value.get(price);

      newBuyOrders.push({
        price: price,
        buyVolume: marketData?.bidVolume || 0,
        sellVolume: 0,
        level: i.toString()
      });
    }

    // 原子性更新数据
    sellOrders.value = newSellOrders;
    buyOrders.value = newBuyOrders;

    console.log('✅ 档位数据生成完成:', {
      卖盘档位: newSellOrders.length,
      买盘档位: newBuyOrders.length,
      价格范围: `${newBuyOrders[newBuyOrders.length - 1]?.price} - ${newSellOrders[0]?.price}`
    });
  } finally {
    isUpdatingOrders = false;
  }
}

// 生成空的价格档位以铺满表格
const generateEmptyPriceOrders = () => {
  // 生成空的卖盘数据 - 按倒序排列
  const emptySellOrders: OrderData[] = []
  for (let i = 1; i <= PRICE_LEVELS.SELL_LEVELS; i++) {
    emptySellOrders.push({
      price: 0,
      buyVolume: 0,
      sellVolume: 0,
      level: i.toString()
    })
  }
  // 卖盘倒序排列
  emptySellOrders.reverse()

  // 生成空的买盘数据
  const emptyBuyOrders: OrderData[] = []
  for (let i = 1; i <= PRICE_LEVELS.BUY_LEVELS; i++) {
    emptyBuyOrders.push({
      price: 0,
      buyVolume: 0,
      sellVolume: 0,
      level: i.toString()
    })
  }

  sellOrders.value = emptySellOrders
  buyOrders.value = emptyBuyOrders
}

// 缩放功能
const zoomIn = () => {
  fontSize.value = Math.min(fontSize.value + 1, 20)
  cellHeight.value = Math.min(cellHeight.value + 2, 30)
}

const zoomOut = () => {
  fontSize.value = Math.max(fontSize.value - 1, 8)
  cellHeight.value = Math.max(cellHeight.value - 2, 12)
}

// 判断是否选中
const isSelected = (type: 'sell' | 'buy', field: 'cancel' | 'buy' | 'sell' | 'price', index: number) => {
  return selectedCell.value?.type === type &&
         selectedCell.value?.field === field &&
         selectedCell.value?.index === index
}

// 处理撤单点击
const handleCancelClick = (type: 'sell' | 'buy', data: OrderData, index: number) => {
  selectedCell.value = {
    type,
    field: 'cancel',
    value: data.price,
    data,
    index
  }

  console.log('点击撤单:', {
    type: type === 'sell' ? '卖盘' : '买盘',
    price: data.price,
    level: data.level
  })

  // 执行撤单操作
  cancelOrder()
}

// 处理下单点击
const handleOrderClick = (orderType: 'buy' | 'sell', data: OrderData, index: number, event?: MouseEvent) => {
  // 根据鼠标按键决定下单数量
  const isRightClick = event?.button === 2 // 右键
  const orderQuantity = isRightClick ? heavyOrderQuantity.value : lightOrderQuantity.value
  const orderMode = isRightClick ? '重仓' : '轻仓'

  selectedCell.value = {
    type: orderType,
    field: orderType,
    value: data.price,
    data,
    index,
    quantity: orderQuantity // 添加数量信息
  }

  // 自动填入价格
  orderPrice.value = data.price

  console.log('点击下单:', {
    orderType: orderType === 'buy' ? '买单' : '卖单',
    price: data.price,
    level: data.level,
    mode: orderType,
    quantity: orderQuantity,
    orderMode: orderMode,
    mouseButton: isRightClick ? '右键' : '左键'
  })

  // 执行下单操作
  placeOrder()
}

// 下单操作
const placeOrder = async () => {
  if (!selectedCell.value) return

  const { type, quantity } = selectedCell.value

  // 使用selectedCell中的数量，如果没有则使用轻仓数量作为默认值
  const orderQuantity = quantity || lightOrderQuantity.value

  try {

    // 获取当前合约代码
    const contractCode = currentContract.value?.code || panelContract.value?.code

    if (!contractCode) {
      console.warn('请先选择合约')
      return
    }

    // 构建订单请求
    const orderRequest: OrderRequest = {
      instrument_id: contractCode, // 使用当前选中的合约
      direction: type === 'buy' ? '0' : '1', // 0=买入, 1=卖出
      price: orderPrice.value,
      volume: orderQuantity,
      order_type: '2', // 2=限价单 (1=市价单)
      offset_flag: '0', // 0=开仓
      hedge_flag: '1', // 1=投机
      time_condition: '3', // 3=当日有效
      volume_condition: '1' // 1=任何数量
    }

    console.log('📤 发送下单请求:', orderRequest)

    // 发送下单请求
    const result = await ctpService.insertOrder(orderRequest)

    if (result.success) {
      const orderMode = quantity === heavyOrderQuantity.value ? '重仓' : '轻仓'
      console.log(`✅ ${orderMode}下单成功: ${type === 'buy' ? '买入' : '卖出'} ${orderQuantity}手 @${orderPrice.value}`, result.data)

      // 清除选择
      clearSelection()
    } else {
      console.error(`❌ 下单失败: ${result.error}`)
    }

  } catch (error) {
    console.error('❌ 下单异常:', error)
  }
}

// 撤单操作
const cancelOrder = async () => {
  if (!selectedCell.value) return

  const { type, data } = selectedCell.value

  try {

    // 注意：这里需要有实际的订单引用号才能撤单
    // 在真实场景中，应该从订单列表中获取要撤销的订单引用号
    const orderRef = `order_${Date.now()}` // 临时的订单引用号，实际应该从订单管理中获取

    console.log('📤 发送撤单请求:', { orderRef, type, level: data.level })

    // 发送撤单请求
    const result = await ctpService.cancelOrder(orderRef)

    if (result.success) {
      console.log(`✅ 撤单成功: ${type === 'sell' ? '卖盘' : '买盘'} 档位${data.level}`, result.data)

      // 清除选择
      clearSelection()
    } else {
      console.error(`❌ 撤单失败: ${result.error}`)
    }

  } catch (error) {
    console.error('❌ 撤单异常:', error)
  }
}

// 清除选择
const clearSelection = () => {
  selectedCell.value = null
}

// 获取总量显示
const getTotalVolume = (item: OrderData) => {
  // 显示买量和卖量的总和
  const buyVol = item.buyVolume || 0
  const sellVol = item.sellVolume || 0
  const total = buyVol + sellVol
  return total > 0 ? total : ''
}



// 实时更新当前价格（固定布局，无需滚动）
const updateCurrentPrice = (newPrice: number) => {
  const oldPrice = currentPrice.value
  const newPriceRounded = Math.round(newPrice)

  // 更新价格方向
  if (newPriceRounded > oldPrice) {
    priceDirection.value = 'up'
  } else if (newPriceRounded < oldPrice) {
    priceDirection.value = 'down'
  } else {
    priceDirection.value = 'neutral'
  }

  currentPrice.value = newPriceRounded

  // 如果价格发生变化，重新生成档位数据
  if (Math.abs(oldPrice - currentPrice.value) >= 1) {
    // 重新生成以新价格为中心的档位数据
    generatePriceOrders(currentPrice.value)
  }

  // 3秒后重置方向为中性
  setTimeout(() => {
    priceDirection.value = 'neutral'
  }, 3000)
}

// 行情数据监听器引用，用于在组件卸载时清理
let marketDataListener: ((data: any) => void) | null = null

// 初始化窗口ID
const initializeWindowId = () => {
  const currentWindow = getCurrentWebviewWindow()
  windowId.value = currentWindow.label
  console.log(`🪟 [WINDOW] 初始化窗口ID: ${windowId.value}`)
}

// 当前订阅的合约代码列表
const subscribedContracts = ref<string[]>([])

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {

  switch (event.key) {
    case '+':
    case '=':
      event.preventDefault()
      zoomIn()
      break
    case '-':
      event.preventDefault()
      zoomOut()
      break
    case 'Escape':
      event.preventDefault()
      clearSelection()
      break
    case 'Enter':
      if (selectedCell.value) {
        event.preventDefault()
        placeOrder()
      }
      break
    case 'Delete':
    case 'Backspace':
      if (selectedCell.value) {
        event.preventDefault()
        cancelOrder()
      }
      break

  }
}

// 初始化合约数据和行情
const initMarketData = async () => {
  try {
    console.log('🔍 开始初始化交易面板...')

    // 初始化合约信息
    await initializeContractInfo()

    // 设置为使用真实数据
    console.log('📊 使用CTP真实数据')
    isUsingRealData.value = true

  } catch (error) {
    console.error('❌ 初始化交易面板失败:', error)
  }
}



// 初始化合约信息
const initializeContractInfo = async () => {
  try {
    // 首先检查URL参数中是否有合约代码
    const contractCodeFromUrl = getContractCodeFromUrl()

    if (contractCodeFromUrl) {
      console.log('从URL参数获取合约代码:', contractCodeFromUrl)

      // 根据合约代码查询合约信息
      try {
        const contractInfo = await contractService.getContractByCode(contractCodeFromUrl)
        if (contractInfo) {
          console.log('找到合约信息:', contractInfo)
          panelContract.value = contractInfo
          setCurrentContract(contractInfo)

          // 订阅行情数据
          await queryMarketData(contractCodeFromUrl)
          return
        }
      } catch (error) {
        console.warn('查询合约信息失败:', error)
      }

      // 如果查询合约信息失败，直接使用合约代码订阅行情
      console.log('直接使用合约代码订阅行情:', contractCodeFromUrl)
      await queryMarketData(contractCodeFromUrl)
      return
    }

    // 检查是否有当前选中的合约
    if (currentContract.value) {
      console.log('使用当前选中的合约:', currentContract.value)
      panelContract.value = currentContract.value

      // 订阅选中合约的行情数据
      await queryMarketData(currentContract.value.code)
    } else {
      console.log('未找到选中的合约，等待合约选择')
      // 不订阅任何行情，等待用户选择合约
    }
  } catch (error) {
    console.error('❌ 初始化合约信息失败:', error)
  }
}

// 专门用于窗口关闭时的强制清理函数
const forceCleanupOnWindowClose = async (): Promise<void> => {
  console.log(`🚨 窗口 ${windowId.value} 关闭强制清理开始...`)

  const contractsToCleanup = [...subscribedContracts.value]

  // 立即移除事件监听器，防止继续接收数据（窗口级别）
  if (marketDataListener && windowId.value) {
    ctpService.offWindow('market_data', windowId.value, marketDataListener)
    marketDataListener = null
    console.log(`✅ 强制移除窗口 ${windowId.value} 的行情数据监听器`)
  }

  // 清理该窗口的所有资源
  if (windowId.value) {
    ctpService.cleanupWindow(windowId.value)
  }

  // 如果有订阅的合约，尝试取消订阅（带短超时）
  if (subscribedContracts.value.length > 0) {
    try {
      console.log('🔄 强制取消订阅合约行情:', subscribedContracts.value)

      // 使用更短的超时时间（1.5秒）
      const unsubscribePromise = ctpService.unsubscribeMarketData(subscribedContracts.value)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('窗口关闭超时')), 1500)
      })

      const result = await Promise.race([unsubscribePromise, timeoutPromise]) as any

      if (result.success) {
        console.log(`✅ 窗口关闭：已断开 ${contractsToCleanup.join(', ')} 行情连接`)
      } else {
        console.warn(`⚠️ 窗口关闭：断开行情连接失败 - ${result.error}`)
      }
    } catch (error) {
      console.error('❌ 窗口关闭前取消订阅异常:', error)
      const errorMsg = error instanceof Error ? error.message : '未知错误'

      if (errorMsg.includes('超时')) {
        console.warn('⚠️ 窗口关闭超时，强制清理本地状态')
      } else {
        console.error(`❌ 窗口关闭清理异常: ${errorMsg}`)
      }
    }

    // 强制清空订阅列表
    subscribedContracts.value = []
  }

  console.log('✅ 窗口关闭强制清理完成')
}

// 清理当前行情订阅
const cleanupCurrentSubscription = async (showUserMessage: boolean = false) => {
  const contractsToCleanup = [...subscribedContracts.value] // 创建副本用于日志

  console.log('🧹 开始清理行情订阅:', {
    窗口ID: windowId.value,
    监听器存在: !!marketDataListener,
    订阅合约数量: subscribedContracts.value.length,
    订阅合约列表: contractsToCleanup,
    显示用户消息: showUserMessage
  })

  // 移除现有的行情数据监听器（窗口级别）
  if (marketDataListener && windowId.value) {
    ctpService.offWindow('market_data', windowId.value, marketDataListener)
    marketDataListener = null
    console.log(`✅ 已移除窗口 ${windowId.value} 的行情数据监听器`)
  }

  // 取消订阅现有合约
  if (subscribedContracts.value.length > 0) {
    try {
      console.log('🔄 正在取消订阅合约行情:', subscribedContracts.value)

      // 创建一个带超时的Promise来防止无限等待
      const unsubscribePromise = ctpService.unsubscribeMarketData(subscribedContracts.value)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('取消订阅超时')), 3000) // 3秒超时
      })

      const result = await Promise.race([unsubscribePromise, timeoutPromise]) as any
      if (result.success) {
        console.log('✅ 成功取消订阅所有合约行情:', contractsToCleanup)

        if (showUserMessage) {
          console.log(`✅ 已断开 ${contractsToCleanup.join(', ')} 行情数据连接`)
        }
      } else {
        console.warn(`⚠️ 取消订阅合约失败: ${result.error}`)

        if (showUserMessage) {
          console.warn(`⚠️ 断开行情连接失败`)
        }
      }
    } catch (error) {
      console.error('❌ 取消订阅合约异常:', error)

      if (showUserMessage) {
        const errorMsg = error instanceof Error ? error.message : '未知错误'
        if (errorMsg.includes('超时')) {
          console.warn('⚠️ 由于超时，强制清理本地订阅状态')
        }
      }
    }

    // 无论成功失败都清空已订阅列表，避免状态不一致
    subscribedContracts.value = []
  }
}

// 根据合约代码查询行情数据
const queryMarketData = async (contractCode: string): Promise<boolean> => {
  try {
    console.log(`🔍 查询行情数据: ${contractCode}`)

    // 先清理现有订阅
    await cleanupCurrentSubscription()

    // 订阅新的行情数据
    const subscribeResult = await ctpService.subscribeMarketData([contractCode])

    if (subscribeResult.success) {
      // 添加到已订阅列表
      if (!subscribedContracts.value.includes(contractCode)) {
        subscribedContracts.value.push(contractCode)
      }

      // 创建行情数据监听器（窗口级别）
      marketDataListener = (data: any) => {
        if (data.instrument_id === contractCode) {
          updateMarketData(data)
        }
      }

      // 监听行情数据更新（窗口级别）
      ctpService.onWindow('market_data', windowId.value, marketDataListener)

      return true
    } else {
      return false
    }
  } catch (error) {
    return false
  }
}

// 更新行情数据
const updateMarketData = (data: any) => {
  try {
    console.log(`📊 [${windowId.value}] 更新行情数据:`, data)

    // 更新当前价格
    if (data.last_price && data.last_price > 0) {
      const newPrice = Math.round(data.last_price)
      if (newPrice !== currentPrice.value) {
        // 如果这是第一次设置价格，先生成价格档位
        if (currentPrice.value === 0) {
          currentPrice.value = newPrice
          generatePriceOrders(newPrice)
        } else {
          updateCurrentPrice(newPrice)
        }
      }
    }

    // 更新成交量（真实数据）
    if (data.volume && data.volume > 0) {
      totalVolume.value = data.volume
      console.log('📊 更新成交量:', data.volume)
    }

    // 更新持仓量（真实数据）
    if (data.open_interest && data.open_interest > 0) {
      totalPosition.value = data.open_interest
      console.log('📊 更新持仓量:', data.open_interest)
    }

    // 计算日内持仓变化（真实数据）
    if (data.open_interest && data.pre_open_interest) {
      const positionChange = data.open_interest - data.pre_open_interest
      dailyPositionChange.value = positionChange
      console.log('📊 更新日内持仓变化:', positionChange)
    }

    // 计算价格变化百分比（真实数据）
    if (data.last_price && data.pre_settlement_price && data.pre_settlement_price > 0) {
      const changePercent = ((data.last_price - data.pre_settlement_price) / data.pre_settlement_price) * 100
      priceChangePercent.value = changePercent.toFixed(2)
      console.log('📊 更新价格变化百分比:', changePercent.toFixed(2) + '%')
    }

    // 更新买卖盘数据到marketDataMap
    if (data.bid_price1 && data.bid_volume1) {
      const bidPrice = Math.round(data.bid_price1)
      const existingData = marketDataMap.value.get(bidPrice) || { bidVolume: 0, askVolume: 0 }
      marketDataMap.value.set(bidPrice, {
        bidVolume: data.bid_volume1,
        askVolume: existingData.askVolume
      })
      console.log('📊 更新买一盘:', `价格=${bidPrice}, 量=${data.bid_volume1}`)
    }

    if (data.ask_price1 && data.ask_volume1) {
      const askPrice = Math.round(data.ask_price1)
      const existingData = marketDataMap.value.get(askPrice) || { bidVolume: 0, askVolume: 0 }
      marketDataMap.value.set(askPrice, {
        bidVolume: existingData.bidVolume,
        askVolume: data.ask_volume1
      })
      console.log('📊 更新卖一盘:', `价格=${askPrice}, 量=${data.ask_volume1}`)
    }

    // 如果有买卖盘数据更新，重新生成价格档位
    if ((data.bid_price1 && data.bid_volume1) || (data.ask_price1 && data.ask_volume1)) {
      if (currentPrice.value > 0) {
        generatePriceOrders(currentPrice.value)
      }
    }

    // 更新其他价格信息
    if (data.open_price) {
      console.log('📊 开盘价:', data.open_price)
    }
    if (data.highest_price) {
      console.log('📊 最高价:', data.highest_price)
    }
    if (data.lowest_price) {
      console.log('📊 最低价:', data.lowest_price)
    }

  } catch (error) {
    console.error('❌ 更新行情数据失败:', error)
  }
}



// 监听合约变化，自动切换行情订阅
watch(
  () => currentContract.value,
  async (newContract, oldContract) => {
    if (newContract && newContract.code !== oldContract?.code) {
      console.log('🔄 检测到合约变化:', {
        从: oldContract?.code || '无',
        到: newContract.code
      })

      // 更新面板合约信息
      panelContract.value = newContract

      // 重新订阅新合约的行情数据
      const subscribeSuccess = await queryMarketData(newContract.code)
      if (subscribeSuccess) {
        console.log(`✅ 已切换到 ${newContract.name} (${newContract.code}) 行情`)
      } else {
        console.warn(`⚠️ 切换到 ${newContract.name} 行情失败`)
      }
    }
  },
  { immediate: false } // 不立即执行，避免初始化时重复订阅
)

// 组件挂载和卸载时的事件监听
onMounted(async () => {
  // 首先初始化窗口ID
  initializeWindowId()

  document.addEventListener('keydown', handleKeydown)

  // 初始化合约信息
  initializeContractInfo()

  // 生成空的价格档位以铺满表格
  generateEmptyPriceOrders()

  // 初始化行情数据
  initMarketData()

  // 固定布局，无需滚动

  console.log(`交易面板已挂载，窗口ID: ${windowId.value}`)

  // 监听窗口关闭事件，确保在用户通过窗口控制按钮关闭时也能清理资源
  const currentWindow = getCurrentWebviewWindow()
  await currentWindow.onCloseRequested(async (event) => {
    console.log('🔄 检测到窗口关闭请求，阻止关闭并开始清理行情数据...')

    // 阻止窗口立即关闭
    event.preventDefault()

    try {
      // 使用专门的窗口关闭清理函数
      console.log('⏳ 正在等待行情数据清理完成...')
      await forceCleanupOnWindowClose()

      // 清理完成后，手动关闭窗口
      console.log('🔄 行情数据清理完成，现在关闭窗口...')
      await currentWindow.close()

    } catch (error) {
      console.error('❌ 窗口关闭前清理行情数据失败，强制关闭窗口:', error)

      // 即使清理失败，也要关闭窗口
      setTimeout(async () => {
        await currentWindow.close()
      }, 500) // 给用户500ms时间看到错误消息
    }
  })
})



onUnmounted(async () => {
  console.log(`🔄 交易面板窗口 ${windowId.value} 关闭，开始清理资源...`)

  // 移除键盘事件监听器
  document.removeEventListener('keydown', handleKeydown)

  // 使用统一的清理函数来移除合约行情数据获取
  try {
    // 在组件卸载时不显示用户消息，避免在窗口已关闭时显示消息
    await cleanupCurrentSubscription(false)
    console.log(`✅ 窗口 ${windowId.value} 的合约行情数据获取已完全移除`)
  } catch (error) {
    console.error(`❌ 清理窗口 ${windowId.value} 的合约行情订阅时发生异常:`, error)
    // 在组件卸载时不显示错误消息，因为窗口可能已经关闭
  }

  // 清理该窗口的所有资源
  if (windowId.value) {
    ctpService.cleanupWindow(windowId.value)
  }

  console.log(`✅ 交易面板窗口 ${windowId.value} 关闭，所有资源清理完成`)
})
</script>

<style scoped>
.trading-panel-container {
  padding: 10px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background: #f0f0f0;
}

/* 主交易面板样式 */
.trading-panel {
  display: flex;
  gap: 2px;
  background: #c0c0c0;
  border: 1px solid #808080;
  padding: 2px;
  font-family: 'MS Sans Serif', sans-serif;
  font-size: 11px;
  height: calc(100% - 50px);
  width: 100%;
  box-sizing: border-box;
}

/* 左侧操作列 */
.left-control-panel {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 3px;
  overflow-y: auto;
  flex-shrink: 0;
}

/* 合约代码显示 */
.contract-code-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px 4px;
  text-align: center;
  font-weight: bold;
  font-size: 10px;
  color: #000000;
}



/* 缩放控制 */
.zoom-controls {
  display: flex;
  gap: 1px;
  justify-content: center;
}

.zoom-btn {
  width: 16px;
  height: 16px;
  border: 1px outset #c0c0c0;
  background: #c0c0c0;
  cursor: pointer;
  font-size: 9px;
  font-weight: bold;
  color: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:active {
  border: 1px inset #c0c0c0;
}

/* 价格信息区域 */
.price-info-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.price-change-display {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 2px;
  text-align: center;
}

.price-change-value {
  font-size: 10px;
  font-weight: bold;
}

.price-change-value.negative {
  color: #ff0000;
}

.price-change-value.positive {
  color: #008000;
}

/* 市场统计数据 */
.market-stats {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.stat-item {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 2px;
  text-align: center;
  font-size: 9px;
  color: #000000;
}

/* 合约信息样式 */
.contract-info {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 8px;
  margin: 5px 0;
  font-size: 11px;
}

.contract-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2px 0;
}

.contract-detail .label {
  color: #666;
  font-weight: 500;
  min-width: 45px;
}

.contract-detail .value {
  color: #333;
  font-family: 'Courier New', monospace;
  font-weight: 600;
}

.contract-detail .value.price {
  color: #007bff;
}

.contract-detail .value.change.positive {
  color: #dc3545;
}

.contract-detail .value.change.negative {
  color: #28a745;
}

.contract-detail .value.change.neutral {
  color: #6c757d;
}



/* 红蓝数值显示 */
.zero-values {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.zero-value {
  height: 18px;
  line-height: 18px;
  text-align: center;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border: 1px inset #c0c0c0;
}

.zero-value.red {
  background: #ff0000;
}

.zero-value.blue {
  background: #0000ff;
}

/* 下单输入框 */
.order-inputs {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.input-group {
  display: flex;
  justify-content: center;
}

.order-input {
  width: 60px;
  height: 18px;
  padding: 1px 3px;
  border: 1px inset #c0c0c0;
  background: #ffffff;
  text-align: center;
  font-size: 10px;
  color: #000000;
  font-family: 'MS Sans Serif', sans-serif;
}

.order-input:focus {
  outline: none;
  border: 1px inset #0000ff;
}

/* 去掉input number类型的上下箭头 */
.order-input::-webkit-outer-spin-button,
.order-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox浏览器去掉上下箭头 */
.order-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* 订单类型选择 */
.order-type-group {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.radio-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.radio-label input[type="radio"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.radio-text {
  user-select: none;
}

/* 交易选项 */
.order-options {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 9px;
  color: #000000;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 4px;
  width: 12px;
  height: 12px;
}

.checkbox-text {
  user-select: none;
}

/* 持仓信息 */
.position-info-section {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.position-line {
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 1px 3px;
  font-size: 9px;
  color: #000000;
  text-align: left;
}

/* 盈亏显示 */
.pnl-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  background: #ffffff;
  border: 1px inset #c0c0c0;
  padding: 3px;
  margin-top: auto;
}

.pnl-value {
  font-size: 12px;
  font-weight: bold;
  color: #000000;
}

.pnl-letter {
  font-size: 14px;
  font-weight: bold;
  color: #000000;
}

/* 右侧表格区域 */
.price-table-container {
  flex: 1;
  background: #c0c0c0;
  border: 1px inset #c0c0c0;
  display: flex;
  flex-direction: column;
  min-width: 0;
  max-width: 170px; /* 增加宽度以容纳5列 */
}

.price-table {
  flex: 1;
  overflow: hidden; /* 禁用滚动 */
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* 卖盘和买盘区域 */
.sell-orders-section {
  flex: 1;
  background: #c0c0c0;
  display: flex;
  flex-direction: column; /* 卖盘正常排列，数据已在逻辑层倒序 */
  overflow: hidden;
  border-top: 1px solid #808080;
}



.buy-orders-section {
  flex: 1;
  background: #c0c0c0;
  display: flex;
  flex-direction: column; /* 买盘从上往下排列，最高价在顶部 */
  overflow: hidden;
  border-bottom: 1px solid #808080;
}



.price-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr; /* 五列等宽 */
  font-size: v-bind(fontSize + 'px');
  font-family: 'MS Sans Serif', sans-serif;
  background-color: #c0c0c0;
  align-items: center; /* 垂直居中对齐 */
  height: 18px; /* 默认高度18px */
  min-height: 18px;
  >div {
    height: 18px; /* 默认高度18px */
    min-height: 18px;
  }
}

/* 中间分隔线（黑色横条固定居中） */
.price-separator {
  height: 2px;
  background: #000000;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  border-left: 1px solid #808080;
  border-right: 1px solid #808080;
  flex-shrink: 0; /* 防止被压缩 */
  z-index: 10; /* 确保在最上层 */
}

/* 卖盘行样式 - 只有价格列显示红色 */
.sell-row .price-col {
  background: #ff0000; /* 价格列红色背景 */
  color: #ffffff;
}

/* 买盘行样式 - 只有价格列显示蓝色 */
.buy-row .price-col {
  background: #0000ff; /* 价格列蓝色背景 */
  color: #ffffff;
}

/* 表格列样式 */
.cancel-col {
  text-align: center;
  border-left: 1px solid #808080;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  background: #c0c0c0;
  cursor: pointer;
  font-weight: bold;
  color: #000000;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-col:hover {
  background: #a0a0a0;
}

/* 买量列 */
.buy-volume-col {
  text-align: center;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  cursor: pointer;
  color: #000000;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

.buy-volume-col:hover {
  background: rgba(0, 0, 255, 0.1);
}

/* 卖量列 */
.sell-volume-col {
  text-align: center;
  border-right: 1px solid #808080;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  cursor: pointer;
  color: #000000;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

.sell-volume-col:hover {
  background: rgba(255, 0, 0, 0.1);
}

/* 总量列 */
.total-volume-col {
  text-align: center;
  border-top: 1px solid #808080;
  border-bottom: 1px solid #808080;
  border-right: 1px solid #808080;
  font-size: 8px;
  font-weight: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  background: inherit;
}

/* 移除当前价格行样式，使用中间黑线分隔 */

.clickable {
  cursor: pointer;
  transition: all 0.1s ease;
}

.clickable.active {
  background: #008000 !important;
  color: #ffffff !important;
  font-weight: bold;
  border: 1px inset #008000 !important;
}





/* 滚动条样式 */
.price-table::-webkit-scrollbar,
.left-control-panel::-webkit-scrollbar {
  width: 5px;
}

.price-table::-webkit-scrollbar-track,
.left-control-panel::-webkit-scrollbar-track {
  background: #c0c0c0;
  border-radius: 4px;
}

.price-table::-webkit-scrollbar-thumb,
.left-control-panel::-webkit-scrollbar-thumb {
  background: #808080;
  border-radius: 4px;
}

.price-table::-webkit-scrollbar-thumb:hover,
.left-control-panel::-webkit-scrollbar-thumb:hover {
  background: #606060;
}
</style>
